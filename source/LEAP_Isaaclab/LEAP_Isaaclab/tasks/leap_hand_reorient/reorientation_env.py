# --------------------------------------------------------
# LEAP Hand: Low-Cost, Efficient, and Anthropomorphic Hand for Robot Learning
# https://arxiv.org/abs/2309.06440
# Copyright (c) 2025 <PERSON>, Sri Anumakonda
# Licensed under The MIT License [see LICENSE for details]
# --------------------------------------------------------
# Based on:
# https://github.com/isaac-sim/IsaacLab/blob/main/source/isaaclab_tasks/isaaclab_tasks/direct/inhand_manipulation/inhand_manipulation_env.py
# --------------------------------------------------------

from __future__ import annotations

import numpy as np
import torch
from collections.abc import Sequence
from typing import TYPE_CHECKING
import sys
import math

import isaaclab.sim as sim_utils
from isaaclab.assets import Articulation, RigidObject
from isaaclab.envs import DirectRLEnv
from isaaclab.markers import VisualizationMarkers
from isaaclab.sim.spawners.from_files import GroundPlaneCfg, spawn_ground_plane
from isaaclab.utils.math import matrix_from_quat, quat_conjugate, quat_from_angle_axis, quat_mul, sample_uniform, saturate, euler_xyz_from_quat, quat_from_euler_xyz
import time
if TYPE_CHECKING:
    from LEAP_Isaaclab.tasks.leap_hand_reorient.leap_hand_env_cfg import LeapHandEnvCfg

from LEAP_Isaaclab.utils import adr_utils, obs_utils
from LEAP_Isaaclab.utils.adr import LeapHandADR

# ReorientationEnv: 用于手部对物体进行旋转/重定向任务的环境类
# 该类基于 DirectRLEnv 并封装了手部、被操控物体、目标以及奖励计算等逻辑。
class ReorientationEnv(DirectRLEnv):
    cfg: LeapHandEnvCfg

    def __init__(self, cfg: LeapHandEnvCfg, render_mode: str | None = None, **kwargs):
        # 初始化父类（DirectRLEnv），并在其上构建本任务的特定缓冲区和变量
        super().__init__(cfg, render_mode, **kwargs)

        # 记录手的自由度数量（关节数量）
        self.num_hand_dofs = self.hand.num_joints

        # 位置目标的缓冲区（用于位置控制目标）
        self.hand_dof_targets = torch.zeros((self.num_envs, self.num_hand_dofs), dtype=torch.float, device=self.device)
        # 上一时刻和当前的目标，用于相对/绝对动作处理
        self.prev_targets = torch.zeros((self.num_envs, self.num_hand_dofs), dtype=torch.float, device=self.device)
        self.cur_targets = torch.zeros((self.num_envs, self.num_hand_dofs), dtype=torch.float, device=self.device)

        # 获取需要受控的关节索引（按配置中的关节名映射）
        self.actuated_dof_indices = [self.hand.joint_names.index(j) for j in self.cfg.actuated_joint_names]
        self.actuated_dof_indices.sort()

        # 指尖（触点）对应的 body 索引列表，用于计算触点到物体的距离等
        self.finger_bodies = list()
        for body_name in self.cfg.fingertip_body_names:
            self.finger_bodies.append(self.hand.body_names.index(body_name))
        self.finger_bodies.sort()
        self.num_fingertips = len(self.finger_bodies)
        
        # 读取关节位置上下限（用于约束动作映射/饱和）
        joint_pos_limits = self.hand.root_physx_view.get_dof_limits().to(self.device)
        self.hand_dof_lower_limits = joint_pos_limits[..., 0]
        self.hand_dof_upper_limits = joint_pos_limits[..., 1]

        # 用于跟踪何时需要重置目标（例如达到一次成功后旋转目标）
        self.reset_goal_buf = torch.zeros(self.num_envs, dtype=torch.bool, device=self.device)
        # 用于比较物体位置的参考点（初始抓取位置），稍微上移一个小偏差
        self.in_hand_pos = self.object.data.default_root_state[:, 0:3].clone()
        self.in_hand_pos[:, 2] += 0.01
        
        # 连续 z 轴旋转参数：每次目标旋转的角度增量（用于环形目标）
        self.target_z_angle = torch.full((self.num_envs,), 2 * math.pi / self.cfg.z_rotation_steps, dtype=torch.float, device=self.device)
        
        # 目标位置和朝向（初始化为单位四元数和默认位置）
        self.goal_rot = torch.zeros((self.num_envs, 4), dtype=torch.float, device=self.device)
        self.goal_rot[:, 0] = 1.0  # 单位四元数（无旋转）
        self.goal_pos = torch.zeros((self.num_envs, 3), dtype=torch.float, device=self.device)
        self.goal_pos[:, :] = torch.tensor([-0.2, -0.45, 0.68], device=self.device)
        
        # 可视化目标标记器（用于渲染目标位姿）
        self.goal_markers = VisualizationMarkers(self.cfg.goal_object_cfg)

        # 成功计数与连续成功计数（用于 ADR 和统计）
        self.successes = torch.zeros(self.num_envs, dtype=torch.float, device=self.device)
        self.consecutive_successes = torch.zeros(1, dtype=torch.float, device=self.device)

        # 覆盖默认关节位置（用于初始化手的姿态），并重复到每个 environment
        self.override_default_joint_pos = torch.tensor([[0.000, 0.500, 0.000, 0.000, 
                                                        -0.750, 1.300, 0.000, 0.750, 
                                                         1.750, 1.500, 1.750, 1.750, 
                                                         0.000, 1.000, 0.000, 0.000]], device=self.device).repeat(self.num_envs, 1)

        # 物体状态的缓存：位置、线速度、角速度、朝向（四元数）
        self.object_pos = torch.zeros((self.num_envs, 3), dtype=torch.float, device=self.device)
        self.object_linvel = torch.zeros((self.num_envs, 3), dtype=torch.float, device=self.device)
        self.object_angvel = torch.zeros((self.num_envs, 3), dtype=torch.float, device=self.device)
        self.object_rot = torch.zeros((self.num_envs, 4), dtype=torch.float, device=self.device)
        self.object_rot[:, 0] = 1.0 

        # 观测历史缓冲区（用于时间序列输入 / 历史信息）
        self.obs_hist_buf = torch.zeros((self.num_envs, self.cfg.observation_space // self.cfg.hist_len, self.cfg.hist_len), device=self.device, dtype=torch.double)            
        self.output_obs_hist_buf = torch.zeros(self.cfg.scene.num_envs, self.cfg.observation_space // self.cfg.hist_len, self.cfg.hist_len, device=self.cfg.sim.device, dtype=torch.double)
            
        # 单位向量张量（用于按轴生成随机旋转或旋转运算）
        self.x_unit_tensor = torch.tensor([1, 0, 0], dtype=torch.float, device=self.device).repeat((self.num_envs, 1))
        self.y_unit_tensor = torch.tensor([0, 1, 0], dtype=torch.float, device=self.device).repeat((self.num_envs, 1))
        self.z_unit_tensor = torch.tensor([0, 0, 1], dtype=torch.float, device=self.device).repeat((self.num_envs, 1))

        # 每个 env 随机化的最大步长（以 time step 为单位），用于变长 episode
        self.randomized_episode_lengths = torch.randint(int(self.cfg.min_episode_length_s / (self.cfg.sim.dt * self.cfg.decimation)), self.max_episode_length + 1, (self.num_envs,), dtype=torch.int32, device=self.device)

        # ADR（自动域随机化）相关初始化
        if self.cfg.enable_adr:
            # LeapHandADR 管理自定义参数和随机化范围
            self.leap_adr = LeapHandADR(self.event_manager, 
                                                    self.cfg.adr_cfg_dict, 
                                                    self.cfg.adr_custom_cfg_dict)
            self.step_since_last_dr_change = 0
            self.leap_adr.set_num_increments(self.cfg.starting_adr_increments)
            adr_utils.init_adr_obs_act_noise(self)

            # 使用 ADR 时，需要扩展历史缓冲以容纳观测延迟
            self.obs_hist_buf = torch.zeros(self.num_envs, self.cfg.observation_space // self.cfg.hist_len, self.cfg.hist_len + self.cfg.obs_max_latency, device=cfg.sim.device, dtype=torch.float)
            self.obs_latency = torch.empty((self.num_envs, self.cfg.obs_per_timestep), device =self.cfg.sim.device)
            self.act_latency = torch.empty((self.num_envs, self.cfg.action_space), device =self.cfg.sim.device)
            self.act_hist_buf = torch.zeros(self.num_envs, self.cfg.action_space, self.cfg.act_max_latency + 1, device=self.cfg.sim.device, dtype=torch.float)

            print("starting ranges: ")
            print(self.leap_adr.print_params())
        
        # extras 用于存储额外日志信息，确保存在该字典结构
        if not hasattr(self, "extras") or self.extras is None:
            self.extras = {}
        if "log" not in self.extras:
            self.extras["log"] = {}

        # 将 sim/real 索引映射打印/计算一次，用于后续外部接口映射
        self.sim_real_indices()

    def _setup_scene(self):
        # 创建手和物体的 Articulation/RigidObject 实例并加入场景
        self.hand = Articulation(self.cfg.robot_cfg)
        self.object = RigidObject(self.cfg.object_cfg)
        # 添加地面平面（视觉和物理）
        spawn_ground_plane(prim_path="/World/ground", cfg=GroundPlaneCfg())
        # 克隆并复制环境实例（批量 environments）
        self.scene.clone_environments(copy_from_source=False)
        # 将对象注册到场景，以便 EventManager 能够随机化
        self.scene.articulations["robot"] = self.hand
        self.scene.rigid_objects["object"] = self.object
        # 添加全局环境光（用于渲染）
        light_cfg = sim_utils.DomeLightCfg(intensity=2000.0, color=(0.75, 0.75, 0.75))
        light_cfg.func("/World/Light", light_cfg)

    def _pre_physics_step(self, actions: torch.Tensor) -> None:
        # 在物理步之前处理动作：复制并插入动作噪声/延迟（如果启用 ADR）
        self.actions = actions.clone()

        if self.cfg.enable_adr:
            # 从 ADR 获取手部动作噪声幅度并添加高斯噪声
            hand_noise = self.leap_adr.get_custom_param_value("robot_action_noise", "hand_noise")
            if hand_noise > 0:
                noise = torch.randn_like(actions) * hand_noise
                self.actions = actions + noise
            # 创建动作延迟（如果配置中启用）
            self.actions = obs_utils.create_action_latency(self, self.actions)

        # 限制动作范围到 [-1, 1]
        self.actions = torch.clamp(self.actions, -1.0, 1.0)

    def _apply_action(self) -> None:
        # 将动作映射到关节目标：支持相对(relative)和绝对(absolute)两种控制模式
        if self.cfg.action_type=="relative":
            # 相对控制：基于上一目标加上移动平均后的动作增量
            targets = self.prev_targets[:, self.actuated_dof_indices] + self.cfg.act_moving_average * self.actions
            self.cur_targets[:, self.actuated_dof_indices] = saturate(
                targets,
                self.hand_dof_lower_limits[:, self.actuated_dof_indices],
                self.hand_dof_upper_limits[:, self.actuated_dof_indices],
            )
        elif self.cfg.action_type=="absolute":
            # 绝对控制：先将动作缩放到关节限制范围内，再做平滑（移动平均）
            self.cur_targets[:, self.actuated_dof_indices] = scale(
                self.actions,
                self.hand_dof_lower_limits[:, self.actuated_dof_indices],
                self.hand_dof_upper_limits[:, self.actuated_dof_indices],
            )
            self.cur_targets[:, self.actuated_dof_indices] = (
                self.cfg.act_moving_average * self.cur_targets[:, self.actuated_dof_indices]
                + (1.0 - self.cfg.act_moving_average) * self.prev_targets[:, self.actuated_dof_indices]
            )
            self.cur_targets[:, self.actuated_dof_indices] = saturate(
                self.cur_targets[:, self.actuated_dof_indices],
                self.hand_dof_lower_limits[:, self.actuated_dof_indices],
                self.hand_dof_upper_limits[:, self.actuated_dof_indices],
            )
        else:
            raise ValueError(f"Unsupported action type: {self.cfg.action_type}. Must be relative or absolute.")

        # 更新 prev_targets 以供下一步使用
        self.prev_targets[:, self.actuated_dof_indices] = self.cur_targets[:, self.actuated_dof_indices]

        # 如果启用 ADR，可能会在物体上施加外力/扭矩以增强鲁棒性
        if self.cfg.enable_adr:
            adr_utils.apply_object_wrench(self, self.object, "object")

        # 将关节位置目标写回仿真（位置控制接口）
        self.hand.set_joint_position_target(
            self.cur_targets[:, self.actuated_dof_indices], joint_ids=self.actuated_dof_indices
        )

    def _update_continuous_z_rotation(self, goal_env_ids):        
        # 对目标姿态在 Z 轴方向上累加旋转（用于连续目标旋转策略）
        add_rot = quat_from_angle_axis(self.target_z_angle, self.z_unit_tensor)
        # 将增量四元数乘到目标四元数上（注意广播与索引）
        self.goal_rot[goal_env_ids] = quat_mul(add_rot[goal_env_ids], self.goal_rot[goal_env_ids])
        
        # 更新目标可视化位置（相对于各个 env 原点）
        goal_pos = self.goal_pos + self.scene.env_origins
        self.goal_markers.visualize(goal_pos, self.goal_rot)

    def _get_observations(self) -> dict:
        # 获取手关节位置并反缩放到 [-1,1] 表示域（与 scale/unscale 互为反函数）
        frame = unscale(self.hand_dof_pos,
                    self.hand_dof_lower_limits,
                    self.hand_dof_upper_limits)  
        # 根据配置决定是否在观测中加入当前动作目标（cur_targets）
        if self.cfg.store_cur_actions:
            frame = torch.cat((frame, self.cur_targets[:]), dim=-1)  

        # 更新观测历史（左移并追加最新帧）
        self.obs_hist_buf[:, :, :-1] = self.obs_hist_buf[:, :, 1:]
        self.obs_hist_buf[:, :, -1] = frame    
        obs = self.obs_hist_buf.transpose(1, 2).reshape(self.num_envs, -1)   
        return {"policy": obs.float()}

    def _get_rewards(self) -> torch.Tensor:
        # 计算姿态差惩罚：当前目标与覆盖默认姿态之间的平方差之和
        pose_diff_penalty = ((self.cur_targets[:, self.actuated_dof_indices] - self.override_default_joint_pos) ** 2).sum(-1)
        # 扭矩惩罚：使用仿真计算得到的关节/执行器力矩平方和
        torque_penalty = (self.hand.data.computed_torque ** 2).sum(-1)

        (
            total_reward,
            self.reset_goal_buf,
            self.successes[:],
            self.consecutive_successes[:],
        ) = compute_rewards(
            self.reset_buf,
            self.reset_goal_buf,
            self.successes,
            self.consecutive_successes,
            self.max_episode_length,
            self.fingertip_pos,
            self.object_pos,
            self.object_rot,
            self.in_hand_pos,
            self.goal_rot,
            self.object_linvel,
            self.object_angvel,
            self.cfg.dist_reward_scale,
            self.cfg.rot_reward_scale,
            self.cfg.rot_eps,
            self.actions,
            self.cfg.action_penalty_scale,
            pose_diff_penalty, 
            self.cfg.pose_diff_penalty_scale,
            torque_penalty,
            self.cfg.torque_penalty_scale,
            self.cfg.success_tolerance,
            self.cfg.reach_goal_bonus,
            self.cfg.fall_dist,
            self.cfg.fall_penalty,
            self.cfg.av_factor,
        )

        # 记录日志信息（用于训练监控/调试）
        self.extras["log"]["consecutive_successes"] = self.consecutive_successes.mean() / self.cfg.z_rotation_steps
        self.extras["log"]["pose_diff_penalty"] = pose_diff_penalty.mean() 
        self.extras["log"]["torque_info"] = torque_penalty.mean() 
        self.extras["log"]['object_linvel'] = torch.norm(self.object_linvel, p=1, dim=-1).mean()
        self.extras["log"]['roll'] = self.object_angvel[:, 0].mean()
        self.extras["log"]['pitch'] = self.object_angvel[:, 1].mean()
        self.extras["log"]['yaw'] = self.object_angvel[:, 2].mean()

        # 记录 episode 长度统计信息（秒）
        self.extras["log"]["avg_episode_length_s"] = (self.randomized_episode_lengths.float() * self.cfg.sim.dt * self.cfg.decimation).mean()
        self.extras["log"]["min_episode_length_s"] = (self.randomized_episode_lengths.float() * self.cfg.sim.dt * self.cfg.decimation).min()
        self.extras["log"]["max_episode_length_s"] = (self.randomized_episode_lengths.float() * self.cfg.sim.dt * self.cfg.decimation).max()

        if self.cfg.enable_adr:
            # 计算 ADR 标准（用于自动增加难度）
            adr_criteria = ((self.consecutive_successes / self.cfg.z_rotation_steps) / (self.randomized_episode_lengths.float().mean() * self.cfg.sim.dt * self.cfg.decimation)).float().mean()
            self.extras["log"]["adr_criteria"] = adr_criteria

        # 如果目标被达到（reset_goal_buf），更新目标朝向（连续 z 旋转）并清除标志
        goal_env_ids = self.reset_goal_buf.nonzero(as_tuple=False).squeeze(-1)
        if len(goal_env_ids) > 0:
            self._update_continuous_z_rotation(goal_env_ids)
            self.reset_goal_buf[goal_env_ids] = 0

        return total_reward

    def _get_dones(self) -> tuple[torch.Tensor, torch.Tensor]:
        # 在计算 dones 之前更新中间变量（位置/速度/朝向等）
        self._compute_intermediate_values()
        # 若物体偏离手中参考位置超过阈值则视为坠落
        goal_dist = torch.norm(self.object_pos - self.in_hand_pos, p=2, dim=-1)
        out_of_reach = goal_dist >= self.cfg.fall_dist
        # 超时判断：与随机化 episode 长度比较
        time_out = self.episode_length_buf >= self.randomized_episode_lengths - 1

        # 计算物体和目标在 z 轴上的朝向差异（通过方向向量点积）
        obj_z = matrix_from_quat(self.object_rot)[:, :, 2]
        goal_z = matrix_from_quat(self.goal_rot)[:, :, 2]
        diff = torch.sum(obj_z * goal_z, dim=1)
        # 如果两者几乎反向（点积绝对值小于阈值），则认为物体翻转（flipped）
        flipped = (torch.abs(diff) < 0.5)

        out_of_reach = out_of_reach | flipped

        return out_of_reach, time_out

    def _reset_idx(self, env_ids: Sequence[int] | None):
        # reset 一组 env 的状态，如果 env_ids 为 None 则重置所有环境
        if env_ids is None:
            env_ids = self.hand._ALL_INDICES

        if self.cfg.enable_adr:
            adr_criteria = ((self.consecutive_successes.float().mean() / self.cfg.z_rotation_steps) / (self.randomized_episode_lengths.float().mean() * self.cfg.sim.dt * self.cfg.decimation)).float().mean()

        # 调用父类重置（会重置大部分底层物理状态）
        super()._reset_idx(env_ids)

        # 为被重置的 env 重新采样 episode 长度
        self.randomized_episode_lengths[env_ids] = torch.randint(
            int(self.cfg.min_episode_length_s / (self.cfg.sim.dt * self.cfg.decimation)), 
            self.max_episode_length + 1, 
            (len(env_ids),), 
            dtype=torch.int32, 
            device=self.device
        )

        # 重置物体状态：基于默认状态并应用 env 原点偏移
        object_default_state = self.object.data.default_root_state.clone()[env_ids]
        dof_pos = self.override_default_joint_pos[env_ids] 
        dof_vel = self.hand.data.default_joint_vel[env_ids] 
        
        object_default_state[:, 0:3] += self.scene.env_origins[env_ids]
        # 将物体线速度/角速度部分清零（索引 7: 表示速度部分）
        object_default_state[:, 7:] = torch.zeros_like(self.object.data.default_root_state[env_ids, 7:])

        # ADR 情况下对物体和手做随机化（位置/旋转/关节噪声等）
        if self.cfg.enable_adr:
            x_width = self.leap_adr.get_custom_param_value("object_spawn", "x_width_spawn")
            y_width = self.leap_adr.get_custom_param_value("object_spawn", "y_width_spawn")
            x_rot = self.leap_adr.get_custom_param_value("object_spawn", "x_rotation")
            y_rot = self.leap_adr.get_custom_param_value("object_spawn", "y_rotation")
            z_rot = self.leap_adr.get_custom_param_value("object_spawn", "z_rotation")
            
            # 位移噪声
            if x_width > 0 or y_width > 0:
                pos_noise = sample_uniform(-1.0, 1.0, (len(env_ids), 2), device=self.device)
                object_default_state[:, 0] += pos_noise[:, 0] * x_width
                object_default_state[:, 1] += pos_noise[:, 1] * y_width
            
            # 绕各轴的随机旋转
            if x_rot > 0:
                x_rot_noise = sample_uniform(-1.0, 1.0, (len(env_ids),), device=self.device)
                x_rot_quat = quat_from_angle_axis(x_rot_noise * x_rot, self.x_unit_tensor[env_ids])
                object_default_state[:, 3:7] = quat_mul(x_rot_quat, object_default_state[:, 3:7])
                
            if y_rot > 0:
                y_rot_noise = sample_uniform(-1.0, 1.0, (len(env_ids),), device=self.device)
                y_rot_quat = quat_from_angle_axis(y_rot_noise * y_rot, self.y_unit_tensor[env_ids])
                object_default_state[:, 3:7] = quat_mul(y_rot_quat, object_default_state[:, 3:7])
                
            if z_rot > 0:
                z_rot_noise = sample_uniform(-1.0, 1.0, (len(env_ids),), device=self.device)
                z_rot_quat = quat_from_angle_axis(z_rot_noise * z_rot, self.z_unit_tensor[env_ids])
                object_default_state[:, 3:7] = quat_mul(z_rot_quat, object_default_state[:, 3:7])

            # 关节位置/速度的噪声
            joint_pos_noise_width = self.leap_adr.get_custom_param_value("robot_spawn", "joint_pos_noise")
            joint_vel_noise_width = self.leap_adr.get_custom_param_value("robot_spawn", "joint_vel_noise")

            if joint_pos_noise_width > 0:
                joint_pos_noise = sample_uniform(-1.0, 1.0, (len(env_ids), self.num_hand_dofs), device=self.device)
                dof_pos += joint_pos_noise * joint_pos_noise_width
                
            if joint_vel_noise_width > 0:
                joint_vel_noise = sample_uniform(-1.0, 1.0, (len(env_ids), self.num_hand_dofs), device=self.device)
                dof_vel += joint_vel_noise * joint_vel_noise_width

        # 将重置的位姿和速度写回仿真
        self.object.write_root_pose_to_sim(object_default_state[:, :7], env_ids)
        self.object.write_root_velocity_to_sim(object_default_state[:, 7:], env_ids)

        # 重置手的命令目标与状态
        self.prev_targets[env_ids] = dof_pos
        self.cur_targets[env_ids] = dof_pos
        self.hand_dof_targets[env_ids] = dof_pos
        self.successes[env_ids] = 0

        self.hand.set_joint_position_target(dof_pos, env_ids=env_ids)
        self.hand.write_joint_state_to_sim(dof_pos, dof_vel, env_ids=env_ids)

        # ADR 特有的后处理：更新延迟/噪声等参数
        if self.cfg.enable_adr and len(env_ids) > 0:
            adr_utils.update_adr_obs_act_noise(self, env_ids)

            obs_latency_resets =  self.leap_adr.get_custom_param_value("obs_latency","latency") - torch.randint(0, self.cfg.obs_latency_rand + 1, (len(env_ids),1), device=self.cfg.sim.device)
            obs_latency_resets = torch.maximum(obs_latency_resets, torch.tensor(0))
            self.obs_latency[env_ids, :] = obs_latency_resets.expand(-1, self.cfg.obs_per_timestep)
            
            act_latency_resets = self.leap_adr.get_custom_param_value("action_latency","hand_latency") - torch.randint(0, self.cfg.act_latency_rand + 1, (len(env_ids), 1), device=self.cfg.sim.device)
            act_latency_resets = torch.maximum(act_latency_resets, torch.tensor(0))
            self.act_latency[env_ids, :] = act_latency_resets.expand(-1, self.cfg.action_space)
            
            self.extras["log"]["num_adr_increases"] = self.leap_adr.num_increments()
            
            # 根据 ADR 指标决定是否增加随机化范围
            if self.step_since_last_dr_change >= self.cfg.min_steps_for_dr_change and\
                (adr_criteria  >= self.cfg.min_rot_adr_coeff):
                self.step_since_last_dr_change = 0
                self.leap_adr.increase_ranges()
                self.leap_adr.print_params()
                self.consecutive_successes.fill_(0.0)
            else:
                self.step_since_last_dr_change += 1

            # 是否在该 episode 对物体施加外力（概率决定）
            self.object_mass = self.object.root_physx_view.get_masses().to(device=self.device) 
            self.apply_wrench = torch.where(
                torch.rand(self.num_envs, device=self.device) <= self.cfg.wrench_prob_per_rollout,
                True,
                False)

        # 初始化目标朝向：将物体的 roll/pitch 清零，仅保留 yaw（绕 z 的朝向）
        self._compute_intermediate_values()
        r,p,y = euler_xyz_from_quat(self.object_rot[env_ids])
        r[:].fill_(0.0)
        p[:].fill_(0.0)
        self.goal_rot[env_ids] = quat_from_euler_xyz(r,p,y)

        # 对目标施加一次连续 z 轴旋转（以便开始时目标处于正确相对位姿）
        self._update_continuous_z_rotation(env_ids)

    def _compute_intermediate_values(self):
        # 计算并缓存与手相关的中间量（触点位置、旋转、速度、关节量）
        self.fingertip_pos = self.hand.data.body_pos_w[:, self.finger_bodies]
        self.fingertip_rot = self.hand.data.body_quat_w[:, self.finger_bodies]
        # 将触点位置转换为相对于 env 原点的局部坐标
        self.fingertip_pos -= self.scene.env_origins.repeat((1, self.num_fingertips)).reshape(
            self.num_envs, self.num_fingertips, 3
        )
        self.fingertip_velocities = self.hand.data.body_vel_w[:, self.finger_bodies]

        self.hand_dof_pos = self.hand.data.joint_pos
        self.hand_dof_vel = self.hand.data.joint_vel 

        # 计算并缓存物体的世界坐标位置、四元数和速度信息（减去 env 原点）
        self.object_pos = self.object.data.root_pos_w - self.scene.env_origins
        self.object_rot = self.object.data.root_quat_w #w,x,y,z
        self.object_velocities = self.object.data.root_vel_w
        self.object_linvel = self.object.data.root_lin_vel_w
        self.object_angvel = self.object.data.root_ang_vel_w 
            
    def sim_real_indices(self):
        # 计算仿真关节索引到真实硬件/控制顺序的映射（便于 real-world 接口）
        sim2real_idx_16, _ = self.hand.find_joints(self.cfg.actuated_joint_names, preserve_order=True)
        sim2real_idx_16 = torch.tensor(sim2real_idx_16) - min(sim2real_idx_16)
        real2sim_idx_16 = torch.empty_like(sim2real_idx_16)
        real2sim_idx_16[sim2real_idx_16] = torch.arange(len(sim2real_idx_16))

        print(f"sim2real_indices: {sim2real_idx_16}")
        print(f"real2sim_indices: {real2sim_idx_16}")
            
# -------------------------------
# 以下是一些辅助函数，使用 torch.jit.script 编译以提升性能
# -------------------------------

@torch.jit.script
def scale(x, lower, upper):
    # 将标准化输入 x ∈ [-1, 1] 缩放到实际关节限制 [lower, upper]
    return 0.5 * (x + 1.0) * (upper - lower) + lower


@torch.jit.script
def unscale(x, lower, upper):
    # 将关节实际位置映射回标准化域 [-1, 1]
    return (2.0 * x - upper - lower) / (upper - lower)


@torch.jit.script
def rotation_distance(object_rot, target_rot):
    # 计算物体四元数和目标四元数之间的旋转误差（以弧度为单位）
    # quat_diff = object_rot * conj(target_rot)
    quat_diff = quat_mul(object_rot, quat_conjugate(target_rot))
    # 通过四元数向量部分的范数求得角度： 2 * asin(|vec(quat_diff)|)
    return 2.0 * torch.asin(torch.clamp(torch.norm(quat_diff[:, 1:4], p=2, dim=-1), max=1.0))  # changed quat convention

@torch.jit.script
def compute_rewards(
    reset_buf: torch.Tensor,
    reset_goal_buf: torch.Tensor,
    successes: torch.Tensor,
    consecutive_successes: torch.Tensor,
    max_episode_length: float,
    fingertip_pos: torch.Tensor,
    object_pos: torch.Tensor,
    object_rot: torch.Tensor,
    target_pos: torch.Tensor,
    target_rot: torch.Tensor,
    object_linvel: torch.Tensor,
    object_angvel: torch.Tensor,
    dist_reward_scale: float,
    rot_reward_scale: float,
    rot_eps: float,
    actions: torch.Tensor,
    action_penalty_scale: float,
    pose_diff_penalty: torch.Tensor,
    pose_diff_penalty_scale: float,
    torque_penalty: torch.Tensor,
    torque_penalty_scale: float,
    success_tolerance: float,
    reach_goal_bonus: float,
    fall_dist: float,
    fall_penalty: float,
    av_factor: float,
):

    # 位置距离：物体当前位置到目标参考位置的欧氏距离
    goal_dist = torch.norm(object_pos - target_pos, p=2, dim=-1)
    # 旋转距离：物体朝向与目标朝向的角度差
    rot_dist = rotation_distance(object_rot, target_rot)

    # 基于距离和角度的奖励项
    dist_rew = goal_dist * dist_reward_scale
    rot_rew = 1.0 / (torch.abs(rot_dist) + rot_eps) * rot_reward_scale

    # 动作惩罚（L2）
    action_penalty = torch.sum(actions**2, dim=-1)
    pose_diff_penalty = pose_diff_penalty * pose_diff_penalty_scale
    # 指尖到物体中心距离惩罚（平均到所有指尖）
    fingertip_dist_penalty = torch.norm(fingertip_pos - object_pos.unsqueeze(1), p=2, dim=-1)
    fingertip_dist_penalty = torch.mean(fingertip_dist_penalty, dim=-1)

    # 总奖励：位置 + 旋转 + 动作惩罚 + 姿态偏差惩罚 + 扭矩惩罚（根据超参数加权）
    reward = dist_rew + rot_rew + action_penalty * action_penalty_scale + pose_diff_penalty  + torque_penalty * torque_penalty_scale 

    # 判断哪些 env 达成了目标（角度和位置同时满足阈值）
    goal_resets = torch.where((torch.abs(rot_dist) <= success_tolerance) & (goal_dist <= 0.025), torch.ones_like(reset_goal_buf), reset_goal_buf)
    successes = successes + goal_resets

    # 成功奖励：一旦达到成功阈值就给予额外奖励
    reward = torch.where(goal_resets == 1, reward + reach_goal_bonus, reward)

    # 稳定性奖励：物体绕 z 轴角速度在某一范围内时给予小奖励（抑制失控旋转）
    reward = torch.where((object_angvel[:, 2] > 0.25) & (object_angvel[:, 2] < 1.5), reward + 1, reward)

    # 跌落惩罚：距离目标过远时加上惩罚（fall_penalty 可能为负值）
    reward = torch.where(goal_dist >= fall_dist, reward + fall_penalty, reward)

    # 检查哪些 env 需要重置（例如跌落）
    resets = torch.where(goal_dist >= fall_dist, torch.ones_like(reset_buf), reset_buf)

    # 统计重置数量与在重置前达到的成功数，用于更新连续成功统计
    num_resets = torch.sum(resets)
    finished_cons_successes = torch.sum(successes * resets.float())

    cons_successes = torch.where(
        num_resets > 0,
        av_factor * finished_cons_successes / num_resets + (1.0 - av_factor) * consecutive_successes,
        consecutive_successes,
    )

    return reward, goal_resets, successes, cons_successes