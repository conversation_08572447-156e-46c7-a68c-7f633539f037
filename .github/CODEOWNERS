# Codeowners are designated by their GitHub username. They are
# the people who are responsible for reviewing and approving PRs
# that modify the files that match the pattern.
#
# Codeowners are not the same as contributors. They are not
# automatically added to the PR, but they will be requested to
# review the PR when it is created.
#
# As a general rule, the codeowners are the people who are
# most familiar with the code that the PR is modifying. If you
# are not sure who to add, ask in the issue or in the PR itself.
#
# The format of the file is as follows:
# <file pattern> <codeowners>


# App experience files
# These are the files that are used to launch the app with the correct settings and configurations
/apps/ @kellyguo11 @hhansen-bdai @Mayankm96 @Dhoeller19

# Core Framework
/source/isaaclab/ @Dhoeller19 @Mayankm96 @jsmith-bdai @kellyguo11
/source/isaaclab/isaaclab/actuators @Dhoeller19 @Mayankm96 @nikitardn @jtigue-bdai
/source/isaaclab/isaaclab/app @hhansen-bdai @kellyguo11
/source/isaaclab/isaaclab/assets @jsmith-bdai @Dhoeller19 @kellyguo11 @Mayankm96 @jtigue-bdai
/source/isaaclab/isaaclab/assets/deformable_object @kellyguo11 @Mayankm96 @masoudmoghani
/source/isaaclab/isaaclab/controllers @Mayankm96
/source/isaaclab/isaaclab/envs @jsmith-bdai @Dhoeller19 @kellyguo11 @Mayankm96
/source/isaaclab/isaaclab/envs/manager_based_* @jsmith-bdai @Dhoeller19 @Mayankm96
/source/isaaclab/isaaclab/envs/direct_* @kellyguo11
/source/isaaclab/isaaclab/managers @jsmith-bdai @Dhoeller19 @Mayankm96
/source/isaaclab/isaaclab/sensors @jsmith-bdai @Dhoeller19 @pascal-roth @Mayankm96 @jtigue-bdai
/source/isaaclab/isaaclab/sensors/camera @kellyguo11 @pascal-roth
/source/isaaclab/isaaclab/sensors/contact_sensor @jtigue-bdai
/source/isaaclab/isaaclab/sensors/frame_transformer @jsmith-bdai
/source/isaaclab/isaaclab/sensors/ray_caster @pascal-roth @Dhoeller19
/source/isaaclab/isaaclab/sim @Mayankm96 @jsmith-bdai
/source/isaaclab/isaaclab/sim/simulation_context.py @Dhoeller19 @kellyguo11
/source/isaaclab/isaaclab/terrains @Dhoeller19 @Mayankm96 @nikitardn
/source/isaaclab/isaaclab/utils @Mayankm96 @jsmith-bdai
/source/isaaclab/isaaclab/utils/modifiers @jtigue-bdai
/source/isaaclab/isaaclab/utils/interpolation @jtigue-bdai
/source/isaaclab/isaaclab/utils/noise @jtigue-bdai @kellyguo11
/source/isaaclab/isaaclab/utils/warp @Dhoeller19 @pascal-roth
/source/isaaclab/isaaclab/utils/assets.py @Dhoeller19 @kellyguo11 @Mayankm96
/source/isaaclab/isaaclab/utils/math.py @jsmith-bdai @Dhoeller19 @Mayankm96
/source/isaaclab/isaaclab/utils/configclass.py @Mayankm96 @Dhoeller19

# RL Environment
/source/isaaclab_tasks/ @Dhoeller19 @Mayankm96 @jsmith-bdai @kellyguo11
/source/isaaclab_tasks/isaaclab_tasks/direct @Dhoeller19 @kellyguo11
/source/isaaclab_tasks/isaaclab_tasks/manager_based @Dhoeller19 @Mayankm96 @jsmith-bdai @jtigue-bdai

# Assets
/source/isaaclab_assets/isaaclab_assets/ @Dhoeller19 @pascal-roth @jsmith-bdai

# Standalone Scripts
/scripts/demos/ @jsmith-bdai @jtigue-bdai @Dhoeller19 @kellyguo11 @Mayankm96
/scripts/environments/ @Mayankm96
/scripts/tools/ @jsmith-bdai @Mayankm96
/scripts/tutorials/ @jsmith-bdai @pascal-roth @kellyguo11 @Dhoeller19 @Mayankm96
/scripts/reinforcement_learning/ @jsmith-bdai @Dhoeller19 @kellyguo11 @Mayankm96
/scripts/imitation_learning/ @jsmith-bdai @Dhoeller19 @kellyguo11 @Mayankm96

# Github Actions
# This list is for people wanting to be notified every time there's a change
# related to Github Actions
/.github/ @kellyguo11 @jsmith-bdai

# Visual Studio Code
/.vscode/ @hhansen-bdai @Mayankm96

# Infrastructure (Docker, Docs, Tools)
/docker/ @hhansen-bdai @pascal-roth
/docs/ @jsmith-bdai @Dhoeller19 @kellyguo11 @Mayankm96
/tools/ @hhansen-bdai @jsmith-bdai @Dhoeller19
/isaaclab.* @hhansen-bdai @Dhoeller19 @Mayankm96 @kellyguo11
